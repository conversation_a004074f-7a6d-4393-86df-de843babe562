# Unit Type Constraints Implementation

## Overview

This document describes the implementation of unit-specific enable/disable mechanisms for modes and buttons when creating/editing units, based on the original C# WinForms application logic.

## Features Implemented

### 1. Updated RS485 Support

**Before**: Only 14 unit types supported RS485
**After**: All 33 unit types support RS485 (matching C# original)

```javascript
// All unit types now support RS485
const rs485SupportedTypes = [
  "Room Logic Controller",
  "RLC-I16", "RLC-I20",
  "Bedside-17T", "Bedside-12T", "BSP_R14_OL",
  "RCU-32AO", "RCU-8RL-24AO", "RCU-16RL-16AO", "RCU-24RL-8AO",
  "RCU-11IN-4RL", "RCU-21IN-8RL", "RCU-21IN-8RL-4AO", "RCU-21IN-8RL-4AI",
  "RCU-21IN-8RL-K", "RCU-21IN-10RL", "RCU-21IN-10RL-T", "RCU-30IN-10RL",
  "RCU-48IN-16RL", "RCU-48IN-16RL-4A<PERSON>", "RCU-48IN-16RL-4A<PERSON>", 
  "RCU-48IN-16RL-K", "RCU-48IN-16RL-DL",
  "GNT-EXT-6RL", "GNT-EXT-8RL", "GNT-EXT-10AO", "GNT-EXT-12RL",
  "GNT-EXT-20RL", "GNT-EXT-28AO", "GNT-EXT-12RL-12AO",
  "GNT-EXT-24IN", "GNT-EXT-48IN", "GNT-ETH2KDL"
];
```

### 2. Unit Type Constraints

Based on C# `cbx_Unit_SelectedIndexChanged` logic:

#### Slave-Only Units
These units are forced to Slave mode with disabled checkboxes:
- Bedside-17T, Bedside-12T, BSP_R14_OL
- All GNT-EXT-* units (6RL, 8RL, 10AO, 12RL, 20RL, 28AO, 12RL-12AO, 24IN, 48IN)

**Behavior**:
- Mode: Forced to "Slave" (Master/Stand-Alone disabled)
- CAN Load: Disabled and set to false
- Recovery: Disabled and set to false

#### Regular Units
All other units support all modes:
- Room Logic Controller, RLC-I16, RLC-I20
- All RCU-* units
- GNT-ETH2KDL

**Behavior**:
- Mode: All modes available (Master/Slave/Stand-Alone)
- Default mode: "Stand-Alone"
- CAN Load: Enabled
- Recovery: Enabled

### 3. Mode-Specific Constraints

Based on C# `rbtn_*_CheckedChanged` logic:

#### Master Mode
- CAN Load: **Disabled** and **forced to true**
- Recovery: Enabled

#### Slave Mode  
- CAN Load: **Enabled** and **set to false**
- Recovery: Enabled

#### Stand-Alone Mode
- CAN Load: **Enabled** and **set to false**  
- Recovery: Enabled

## API Reference

### `getUnitTypeConstraints(unitType)`

Returns constraints for a specific unit type:

```javascript
const constraints = getUnitTypeConstraints("Bedside-17T");
// Returns:
{
  modes: {
    master: false,      // Master mode disabled
    slave: true,        // Slave mode enabled
    standAlone: false   // Stand-Alone mode disabled
  },
  defaultMode: "Slave",
  checkboxes: {
    canLoad: false,     // CAN Load checkbox disabled
    recovery: false     // Recovery checkbox disabled
  },
  defaultValues: {
    canLoad: false,     // Default CAN Load value
    recovery: false     // Default Recovery value
  }
}
```

### `getModeConstraints(mode)`

Returns constraints for a specific mode:

```javascript
const constraints = getModeConstraints("Master");
// Returns:
{
  canLoad: {
    enabled: false,     // CAN Load checkbox disabled
    value: true         // CAN Load forced to true
  },
  recovery: {
    enabled: true,      // Recovery checkbox enabled
    value: null         // Keep current value
  }
}
```

### `isSlaveOnlyUnit(unitType)`

Checks if a unit type is slave-only:

```javascript
isSlaveOnlyUnit("Bedside-17T");     // true
isSlaveOnlyUnit("Room Logic Controller"); // false
```

## Implementation Details

### Unit Dialog Logic

1. **Unit Type Selection**: When user selects a unit type:
   - Get unit type constraints
   - Apply default mode and checkbox values
   - Update mode constraints
   - Disable/enable UI elements accordingly

2. **Mode Selection**: When user changes mode:
   - Get mode constraints
   - Apply checkbox enable/disable and values
   - Respect unit type constraints

3. **UI State Management**: 
   - Mode dropdown items are disabled based on unit constraints
   - Checkboxes are disabled with visual feedback (opacity)
   - Labels show disabled state

### Constraint Priority

1. **Unit Type Constraints** (highest priority)
   - Override mode constraints for slave-only units
   - Determine available modes

2. **Mode Constraints** (applied within unit constraints)
   - Control checkbox behavior for each mode
   - Applied only if unit type allows

## Testing

Run the demo script to test constraints:

```bash
node src/demo/unit-constraints-demo.js
```

Run unit tests:

```bash
npm test src/utils/__tests__/rs485-utils.test.js
```

## Migration from Original

This implementation faithfully reproduces the C# WinForms behavior:

- ✅ All unit types support RS485
- ✅ Slave-only units identified correctly  
- ✅ Mode constraints match original logic
- ✅ Checkbox behavior matches original
- ✅ Default values applied correctly
